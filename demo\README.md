# 🎨 MyResume - 现代化个人作品集网站

一个现代化的双页面个人作品集网站演示，采用当代网页设计趋势，具有流畅动画、响应式设计和交互元素。

> **注意**: 此项目为演示版本，展示了现代网页设计的最佳实践和创新交互效果。

## 🌟 功能特色 | Features

- **现代设计 | Modern Design**: 简洁的极简美学设计，采用粗体排版 | Clean, minimalist aesthetic with bold typography
- **流畅动画 | Smooth Animations**: CSS和JavaScript动画提升用户体验 | CSS and JavaScript animations for enhanced user experience
- **自定义光标 | Custom Cursor**: 交互式光标与悬停效果 | Interactive cursor with hover effects
- **加载动画 | Loading Animation**: 引人入胜的加载屏幕和进度指示器 | Engaging loading screen with progress indicator
- **响应式设计 | Responsive Design**: 针对桌面和移动设备优化 | Optimized for desktop and mobile devices
- **双页面设计 | Two Pages**: 主页项目展示和详细关于页面 | Home page with projects showcase and detailed About page
- **交互元素 | Interactive Elements**: 悬停效果、平滑滚动和动画技能条 | Hover effects, smooth scrolling, and animated skill bars

## 📁 项目结构 | Project Structure

```
demo/
├── index.html          # 主页 | Home page
├── about.html          # 关于页面 | About page
├── css/
│   └── styles.css      # 主样式表 | Main stylesheet
├── js/
│   └── main.js         # JavaScript功能 | JavaScript functionality
├── package.json        # npm配置 | npm configuration
└── README.md          # 说明文档 | This file
```

## 🚀 Getting Started

### Prerequisites

- Node.js (version 14.0.0 or higher)
- npm (version 6.0.0 or higher)

### Installation

1. Navigate to the demo directory:
   ```bash
   cd demo
   ```

2. Install dependencies:
   ```bash
   npm install
   ```

3. Start the development server:
   ```bash
   npm start
   ```

4. Open your browser and visit: http://localhost:3000

### Available Scripts

- `npm start` - Start development server and open browser
- `npm run dev` - Start development server and open browser (same as start)
- `npm run serve` - Start server without opening browser
- `npm run build` - Display build message (no build process needed)
- `npm test` - Display test message (no tests configured)

## 🎨 Design Features

### Color Scheme
- **Primary**: #f43a47 (Red)
- **Secondary**: #000000 (Black)
- **Accent**: #ffffff (White)
- **Background**: #f8f8f8 (Light Gray)

### Typography
- **Font Family**: Inter, Helvetica, Arial, sans-serif
- **Weights**: 300, 400, 500, 600, 700, 900
- **Responsive sizing**: Uses clamp() for fluid typography

### Animations
- **Loading Screen**: Progress bar with percentage counter
- **Page Transitions**: Smooth fade-in effects
- **Hover Effects**: Transform and color transitions
- **Scroll Animations**: Intersection Observer API for skill bars and timeline
- **Custom Cursor**: Smooth following cursor with scale effects

## 📱 Pages Overview

### Home Page (index.html)
- **Hero Section**: Large typography with animated background text
- **Projects Section**: Grid layout showcasing featured projects
- **Contact Section**: Call-to-action buttons and contact information

### About Page (about.html)
- **About Hero**: Personal introduction with animated text
- **Skills Section**: Interactive skill bars with progress animations
- **Timeline Section**: Professional experience with visual timeline
- **Contact Section**: Get in touch call-to-action

## 🛠️ Technical Details

### CSS Features
- **CSS Grid & Flexbox**: Modern layout techniques
- **Custom Properties**: CSS variables for consistent theming
- **Media Queries**: Responsive breakpoints
- **Animations**: CSS transitions and keyframe animations
- **Blend Modes**: Mix-blend-mode for cursor effects

### JavaScript Features
- **ES6+ Syntax**: Modern JavaScript features
- **Intersection Observer**: Scroll-triggered animations
- **Event Delegation**: Efficient event handling
- **Smooth Scrolling**: Custom scroll behavior
- **Loading Simulation**: Progress tracking and animation

### Performance Optimizations
- **Debounced Events**: Optimized resize and scroll handlers
- **CSS-only Animations**: Hardware-accelerated transitions
- **Minimal Dependencies**: Lightweight, vanilla JavaScript approach
- **Efficient Selectors**: Optimized DOM queries

## 🌐 Browser Support

- **Chrome**: 60+
- **Firefox**: 60+
- **Safari**: 12+
- **Edge**: 79+

## 📝 Customization

### Changing Colors
Edit the CSS variables in `css/styles.css`:
```css
:root {
  --primary-color: #f43a47;
  --secondary-color: #000000;
  --accent-color: #ffffff;
}
```

### Adding New Sections
1. Add HTML structure to the appropriate page
2. Add corresponding CSS styles
3. Initialize any JavaScript functionality in `js/main.js`

### Modifying Animations
- **CSS Animations**: Edit keyframes and transitions in `css/styles.css`
- **JavaScript Animations**: Modify functions in `js/main.js`

## 🔧 Development Notes

- **Mobile-First**: Responsive design starts with mobile breakpoints
- **Accessibility**: Semantic HTML and keyboard navigation support
- **SEO-Friendly**: Proper heading hierarchy and meta tags
- **Performance**: Optimized for fast loading and smooth animations

## 📄 License

This project is licensed under the MIT License - see the package.json file for details.

## 🤝 Contributing

This is a demo project, but feel free to fork and customize for your own use!

## 📞 Support

For questions or issues, please refer to the code comments or create an issue in the repository.
