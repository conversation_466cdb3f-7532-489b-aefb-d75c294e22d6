<!DOCTYPE html>
<html lang="zh-CN">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Personal Portfolio - Demo</title>
    <link rel="stylesheet" href="css/styles.css">
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <script src="https://cdnjs.cloudflare.com/ajax/libs/gsap/3.12.2/gsap.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/gsap/3.12.2/ScrollTrigger.min.js"></script>

</head>

<body>
    <!-- 移动端警告 -->
    <div class="mobile-warning">
        <div class="mobile-warning-text">
            This webpage is better accessed through a computer browser.
        </div>
    </div>

    <!-- 自定义光标 -->
    <div class="custom-cursor"></div>

    <!-- 加载动画 -->
    <div class="loading-overlay" id="loading-overlay">
        <div class="loading-container">
            <div class="loading-logo">
                <div class="loading-text">LOADING</div>
                <div class="loading-bar">
                    <div class="loading-progress" id="loading-progress"></div>
                </div>
                <div class="loading-percentage" id="loading-percentage">0%</div>
            </div>
        </div>
    </div>

    <!-- 相机取景框 -->
    <div class="camera-frame">
        <div class="frame-corner top-left"></div>
        <div class="frame-corner top-right"></div>
        <div class="frame-corner bottom-left"></div>
        <div class="frame-corner bottom-right"></div>
    </div>

    <!-- 主内容 -->
    <div class="main-content" id="main-content">
        <!-- 导航栏 -->
        <nav class="navbar">
            <div class="nav-logo">
                <div class="morphing-logo" id="morphing-logo">
                    <svg viewBox="0 0 200 100" class="logo-svg" id="svg-logo">
                        <path id="half-circle" d="M50,50 A25,25 0 0,1 100,50" stroke="#000" stroke-width="3"
                            fill="none" />
                        <path id="rect1" d="M120,30 L180,30 L180,70 L120,70 Z" stroke="#000" stroke-width="3"
                            fill="none" />
                        <path id="line1" d="M20,20 L40,80" stroke="#000" stroke-width="3" fill="none" />
                        <path id="line2" d="M40,20 L20,80" stroke="#000" stroke-width="3" fill="none" />
                        <circle id="circle" cx="160" cy="50" r="8" stroke="#000" stroke-width="3" fill="none" />
                    </svg>
                </div>
                <span class="logo-text">X OBERON</span>
            </div>
            <div class="nav-links">
                <a href="#page1" class="nav-link active">HOME</a>
                <a href="#page2" class="nav-link">ABOUT</a>
            </div>
        </nav>

        <!-- 右侧页面导航 -->
        <nav class="page-navigation">
            <div class="nav-dot active" data-page="HOME"></div>
            <div class="nav-dot" data-page="ABOUT"></div>
            <div class="nav-dot" data-page="IDENTITY"></div>
            <div class="nav-dot" data-page="CREATOR"></div>
            <div class="nav-dot" data-page="DEVELOPER"></div>
        </nav>

        <!-- 滚动提示 -->
        <div class="scroll-hint">
            <div class="scroll-arrow"></div>
            <div class="scroll-text">SCROLL</div>
        </div>

        <!-- 滚动容器 -->
        <div class="scroll-container" id="scroll-container">

            <!-- 第1页 - 主页内容 -->
            <section class="page-section hero-section active-section" id="page1">
                <!-- GitHub链接 - 右上角 -->
                <div class="github-link-container">
                    <a href="https://github.com/yourusername" target="_blank" class="github-link">
                        <svg class="github-icon" viewBox="0 0 24 24" fill="currentColor">
                            <path
                                d="M12 0c-6.626 0-12 5.373-12 12 0 5.302 3.438 9.8 8.207 11.387.599.111.793-.261.793-.577v-2.234c-3.338.726-4.033-1.416-4.033-1.416-.546-1.387-1.333-1.756-1.333-1.756-1.089-.745.083-.729.083-.729 1.205.084 1.839 1.237 1.839 1.237 1.07 1.834 2.807 1.304 3.492.997.107-.775.418-1.305.762-1.604-2.665-.305-5.467-1.334-5.467-5.931 0-1.311.469-2.381 1.236-3.221-.124-.303-.535-1.524.117-3.176 0 0 1.008-.322 3.301 1.23.957-.266 1.983-.399 3.003-.404 1.02.005 2.047.138 3.006.404 2.291-1.552 3.297-1.23 3.297-1.23.653 1.653.242 2.874.118 3.176.77.84 1.235 1.911 1.235 3.221 0 4.609-2.807 5.624-5.479 5.921.43.372.823 1.102.823 2.222v3.293c0 .319.192.694.801.576 4.765-1.589 8.199-6.086 8.199-11.386 0-6.627-5.373-12-12-12z" />
                        </svg>
                        <span class="github-text">GITHUB</span>
                    </a>
                </div>

                <div class="hero-background">
                    <!-- 简化的X OBERON水印 -->
                    <div class="watermark-text watermark-1">X OBERON</div>
                    <div class="watermark-text watermark-2">X OBERON</div>
                    <div class="watermark-text watermark-3">X OBERON</div>
                </div>

                <div class="hero-content">
                    <div class="hero-title">
                        <h1 class="main-title">
                            <span class="title-line">HELLO</span>
                            <span class="title-line">I'M</span>
                            <span class="title-line highlight">X OBERON</span>
                        </h1>
                    </div>

                    <div class="hero-subtitle">
                        <p>Creating digital experiences with passion and precision</p>
                    </div>

                    <div class="hero-cta">
                        <button class="cta-button" onclick="scrollToSection('projects')">
                            VIEW MY WORK
                        </button>
                    </div>
                </div>

                <div class="scroll-indicator">
                    <div class="scroll-line"></div>
                    <span class="scroll-text">SCROLL</span>
                </div>
            </section>

            <!-- 第2页 - 关于页面 -->
            <section class="page-section" id="page2">
                <!-- 左半部分 - 文字内容 -->
                <div class="about-left-section">
                    <div class="about-content">
                        <div class="about-title">X Oberon</div>
                        <div class="about-description">
                            <p class="about-line">
                                <span class="normal">Creative developer specializing in</span>
                                <span class="highlight">Interactive Design</span>
                                <span class="highlight">Web Development</span>
                                <span class="normal">and</span>
                                <span class="highlight">Digital Art</span>
                            </p>
                            <div class="about-paragraph">
                                Passionate creator committed to pushing the boundaries of digital experiences.
                                Building immersive web applications that blend technology with artistry.
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 右半部分 - 贴纸装饰 -->
                <div class="about-right-section">
                    <div class="sticker-decorations">
                        <!-- 贴纸组1 - 左上角 -->
                        <div class="sticker-group sticker-group-1">
                            <div class="sticker-pin"></div>
                            <div class="sticker sticker-polaroid sticker-xl">
                                <div class="sticker-content">
                                    <div class="sticker-image">XO</div>
                                    <div class="sticker-label">CREATOR</div>
                                </div>
                            </div>
                        </div>

                        <!-- 贴纸组2 - 右上角 -->
                        <div class="sticker-group sticker-group-2">
                            <div class="sticker-pin"></div>
                            <div class="sticker sticker-badge sticker-xl">
                                <div class="sticker-text">DEVELOPER</div>
                            </div>
                        </div>

                        <!-- 贴纸组3 - 中央 -->
                        <div class="sticker-group sticker-group-3">
                            <div class="sticker-pin"></div>
                            <div class="sticker sticker-polaroid sticker-xxl">
                                <div class="sticker-content">
                                    <div class="sticker-image">WEB</div>
                                    <div class="sticker-label">DESIGNER</div>
                                </div>
                            </div>
                        </div>

                        <!-- 贴纸组4 - 左下角 -->
                        <div class="sticker-group sticker-group-4">
                            <div class="sticker-pin"></div>
                            <div class="sticker sticker-tag sticker-xl">
                                <div class="sticker-text">CREATIVE</div>
                            </div>
                        </div>

                        <!-- 贴纸组5 - 右下角 -->
                        <div class="sticker-group sticker-group-5">
                            <div class="sticker-pin"></div>
                            <div class="sticker sticker-polaroid sticker-xl sticker-tilted">
                                <div class="sticker-content">
                                    <div class="sticker-image">UI</div>
                                    <div class="sticker-label">ARTIST</div>
                                </div>
                            </div>
                        </div>


                    </div>
                </div>
            </section>

            <!-- 第3页 - 水波游泳池效果 -->
            <section class="page-section" id="page3">
                <!-- Three.js 水波容器 -->
                <div class="water-container" id="water-container">
                    <canvas id="water-canvas" class="water-canvas"></canvas>

                    <!-- 水波上的标题内容 -->
                    <div class="water-overlay-content">
                        <div class="water-title">
                            <h1 class="water-main-title">
                                <span class="water-title-line">FLOW</span>
                                <span class="water-title-line">WITH</span>
                                <span class="water-title-line water-highlight">PASSION</span>
                            </h1>
                        </div>
                        <div class="water-subtitle">
                            <p>Interactive Liquid Art Experience</p>
                        </div>
                    </div>
                </div>


            </section>

            <!-- 第4页 - 创作者 (物理小球掉落效果) -->
            <section class="page-section" id="page4">
                <!-- 物理小球画布 -->
                <canvas id="physics-canvas" class="physics-canvas"></canvas>

                <!-- 主要内容 -->
                <div class="hero-content">
                    <div class="hero-title">
                        <h1 class="main-title">
                            <span class="title-line">KEEP</span>
                            <span class="title-line highlight">CREATING</span>
                            <span class="title-line">MAGIC</span>
                        </h1>
                    </div>

                    <div class="hero-subtitle">
                        <p>Interactive Creative Experience</p>
                    </div>
                </div>
            </section>

            <!-- 第5页 - 纯红色背景 -->
            <section class="page-section" id="page5">
            </section>

        </div>
    </div>

    <!-- Three.js 库 -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/three.js/r128/three.min.js"></script>

    <!-- 主JavaScript文件 -->
    <script src="js/main.js"></script>
</body>
</html>