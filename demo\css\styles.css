/* ==================== 全局样式 ==================== */

/* 隐藏滚动条 */
::-webkit-scrollbar {
  display: none;
}

* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  scrollbar-width: none;
  -ms-overflow-style: none;
  background-color: #f43a47;
  font-family: 'Inter', 'Helvetica', 'Arial', sans-serif;
  overflow-x: hidden;
  position: relative;
  width: 100%;
  scroll-behavior: smooth;
  user-select: none;
  cursor: none;
  color: #000;
}

/* ==================== 相机取景框 ==================== */
.camera-frame {
  position: fixed;
  top: 0;
  left: 0;
  width: 100vw;
  height: 100vh;
  pointer-events: none;
  z-index: 9999;
}

.frame-corner {
  position: absolute;
  width: 40px;
  height: 40px;
  border: 3px solid rgba(0, 0, 0, 0.6);
  pointer-events: none;
}

.frame-corner.top-left {
  top: 20px;
  left: 20px;
  border-right: none;
  border-bottom: none;
}

.frame-corner.top-right {
  top: 20px;
  right: 20px;
  border-left: none;
  border-bottom: none;
}

.frame-corner.bottom-left {
  bottom: 20px;
  left: 20px;
  border-right: none;
  border-top: none;
}

.frame-corner.bottom-right {
  bottom: 20px;
  right: 20px;
  border-left: none;
  border-top: none;
}

/* ==================== 滚动容器和页面系统 ==================== */
.scroll-container {
  height: 100vh;
  overflow-y: auto;
  scroll-behavior: smooth;
}

.page-section {
  min-height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
  will-change: transform, opacity;
  transition: all 0.8s cubic-bezier(0.25, 0.46, 0.45, 0.94);
  opacity: 0.2;
  transform: translate3d(0, 60px, 0) scale(0.96);
  backface-visibility: hidden;
  -webkit-backface-visibility: hidden;
}

.page-section:not(.active-section) {
  opacity: 0.2;
  transform: translate3d(0, 60px, 0) scale(0.96);
  pointer-events: none;
  filter: blur(1px);
}

.page-section.active-section {
  opacity: 1;
  transform: translate3d(0, 0, 0) scale(1);
  pointer-events: all;
  filter: blur(0);
}

/* ==================== 右侧翻页提示 ==================== */
.page-navigation {
  position: fixed;
  right: 30px;
  top: 50%;
  transform: translateY(-50%);
  z-index: 1000;
  display: flex;
  flex-direction: column;
  gap: 15px;
}

.nav-dot {
  width: 12px;
  height: 12px;
  border-radius: 50%;
  background: rgba(0, 0, 0, 0.3);
  cursor: pointer;
  transition: all 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94);
  position: relative;
  border: 2px solid transparent;
}

.nav-dot:hover {
  background: rgba(244, 58, 71, 0.6);
  transform: scale(1.3);
}

.nav-dot.active {
  background: #f43a47;
  transform: scale(1.5);
  border-color: rgba(244, 58, 71, 0.3);
  box-shadow: 0 0 20px rgba(244, 58, 71, 0.4);
}

.nav-dot::before {
  content: attr(data-page);
  position: absolute;
  right: 25px;
  top: 50%;
  transform: translateY(-50%);
  background: rgba(0, 0, 0, 0.8);
  color: white;
  padding: 5px 10px;
  border-radius: 4px;
  font-size: 12px;
  font-weight: 500;
  white-space: nowrap;
  opacity: 0;
  pointer-events: none;
  transition: all 0.3s ease;
}

.nav-dot:hover::before {
  opacity: 1;
  right: 30px;
}

/* ==================== 滚动提示 ==================== */
.scroll-hint {
  position: fixed;
  right: 30px;
  bottom: 30px;
  z-index: 1000;
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 10px;
  opacity: 0.7;
  transition: opacity 0.3s ease;
}

.scroll-hint:hover {
  opacity: 1;
}

.scroll-arrow {
  width: 20px;
  height: 20px;
  border-right: 2px solid #000;
  border-bottom: 2px solid #000;
  transform: rotate(45deg);
  animation: scrollBounce 2s infinite;
}

.scroll-text {
  font-size: 12px;
  font-weight: 500;
  letter-spacing: 1px;
  writing-mode: vertical-rl;
  text-orientation: mixed;
}

@keyframes scrollBounce {
  0%, 20%, 50%, 80%, 100% {
    transform: rotate(45deg) translateY(0);
  }
  40% {
    transform: rotate(45deg) translateY(-10px);
  }
  60% {
    transform: rotate(45deg) translateY(-5px);
  }
}

/* ==================== 页面元素初始状态 ==================== */
.title-line {
  opacity: 0;
  transform: translate3d(0, 50px, 0);
  will-change: transform, opacity;
  backface-visibility: hidden;
}

.hero-subtitle, .hero-cta {
  opacity: 0;
  transform: translate3d(0, 30px, 0);
  will-change: transform, opacity;
  backface-visibility: hidden;
}

.about-title {
  opacity: 0;
  transform: translate3d(0, 40px, 0);
  will-change: transform, opacity;
  backface-visibility: hidden;
}

.about-line, .about-paragraph {
  opacity: 0;
  transform: translate3d(0, 30px, 0);
  will-change: transform, opacity;
  backface-visibility: hidden;
}

.identity-title {
  opacity: 0;
  transform: translate3d(-50px, 0, 0) scale(0.8);
  will-change: transform, opacity;
  backface-visibility: hidden;
}

.whoami-title, .whoami-title-right {
  opacity: 0;
  transform: translate3d(0, 40px, 0);
  will-change: transform, opacity;
  backface-visibility: hidden;
}

.page-quote {
  opacity: 0;
  transform: translate3d(50px, 0, 0) scale(0.9);
  will-change: transform, opacity;
  backface-visibility: hidden;
}

.page-quote-right {
  opacity: 0;
  transform: translate3d(-50px, 0, 0) scale(0.9);
  will-change: transform, opacity;
  backface-visibility: hidden;
}

.page-link, .page-link-right {
  opacity: 0;
  transform: translate3d(0, 20px, 0);
  will-change: transform, opacity;
  backface-visibility: hidden;
}

/* ==================== 移动端提示 ==================== */
.mobile-warning {
  display: none;
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: #f43a47;
  z-index: 99999;
  justify-content: center;
  align-items: center;
  text-align: center;
}

.mobile-warning-text {
  color: white;
  font-family: 'Inter', 'Helvetica', 'Arial', sans-serif;
  font-size: 4vw;
  font-weight: bold;
  padding: 20px;
  line-height: 1.4;
}

@media screen and (max-width: 768px), (max-height: 500px) {
  .mobile-warning {
    display: flex !important;
  }
  .main-content {
    display: none !important;
  }
}

/* ==================== 自定义光标 ==================== */
.custom-cursor {
  position: fixed;
  width: 20px;
  height: 20px;
  left: 0;
  top: 0;
  z-index: 2000;
  pointer-events: none;
  transition: all 0.15s cubic-bezier(0.25, 0.46, 0.45, 0.94);
}

.custom-cursor::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 0;
  height: 0;
  border-left: 10px solid #f43a47;
  border-right: 10px solid transparent;
  border-bottom: 16px solid transparent;
  border-top: 4px solid #f43a47;
  filter: drop-shadow(2px 2px 4px rgba(0, 0, 0, 0.3));
  transition: all 0.15s ease;
}

.custom-cursor::after {
  content: '';
  position: absolute;
  top: 2px;
  left: 2px;
  width: 0;
  height: 0;
  border-left: 8px solid #fff;
  border-right: 8px solid transparent;
  border-bottom: 12px solid transparent;
  border-top: 2px solid #fff;
}

.custom-cursor.hover {
  transform: scale(1.2);
}

.custom-cursor.hover::before {
  border-left-color: #ff6b6b;
  border-top-color: #ff6b6b;
  filter: drop-shadow(3px 3px 6px rgba(244, 58, 71, 0.4));
}

.custom-cursor.click {
  transform: scale(0.9);
}

/* ==================== 加载动画 ==================== */
.loading-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: #f43a47;
  z-index: 9999;
  display: flex;
  justify-content: center;
  align-items: center;
  transition: opacity 0.5s ease-out;
}

.loading-overlay.fade-out {
  opacity: 0;
  pointer-events: none;
}

.loading-container {
  text-align: center;
  color: #000;
}

.loading-text {
  font-size: 2rem;
  font-weight: 700;
  margin-bottom: 30px;
  letter-spacing: 3px;
}

.loading-bar {
  width: 200px;
  height: 2px;
  background-color: rgba(0, 0, 0, 0.2);
  margin: 0 auto 20px;
  overflow: hidden;
}

.loading-progress {
  height: 100%;
  background-color: #000;
  width: 0%;
  transition: width 0.3s ease;
}

.loading-percentage {
  font-size: 1.2rem;
  font-weight: 500;
}

/* ==================== 导航栏 ==================== */
.navbar {
  display: none; /* 完全隐藏顶部导航栏 */
}

.nav-logo {
  display: none; /* 隐藏导航栏中的Logo，使用固定定位的变形Logo */
}

.nav-logo .logo-text {
  font-size: 1.5rem;
  font-weight: 700;
  color: #000;
  letter-spacing: 2px;
}

.nav-links {
  display: flex;
  gap: 40px;
}

.nav-link {
  color: #000;
  text-decoration: none;
  font-weight: 500;
  font-size: 1rem;
  letter-spacing: 1px;
  transition: opacity 0.3s ease;
  position: relative;
}

.nav-link:hover,
.nav-link.active {
  opacity: 0.7;
}

.nav-link.active::after {
  content: '';
  position: absolute;
  bottom: -5px;
  left: 0;
  width: 100%;
  height: 2px;
  background-color: #000;
}

/* ==================== 主页样式 ==================== */
.hero-section {
  position: relative;
  height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
  overflow: hidden;
}

/* ==================== GitHub链接样式 ==================== */
.github-link-container {
  position: absolute;
  top: 2rem;
  right: 2rem;
  z-index: 1000;
}

.github-link {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.75rem 1.25rem;
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(0, 0, 0, 0.1);
  border-radius: 50px;
  text-decoration: none;
  color: #000;
  font-weight: 600;
  font-size: 0.875rem;
  letter-spacing: 1px;
  transition: all 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);
  cursor: pointer;
}

.github-link:hover {
  background: rgba(244, 58, 71, 0.1);
  border-color: rgba(244, 58, 71, 0.3);
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(244, 58, 71, 0.15);
}

.github-icon {
  width: 18px;
  height: 18px;
  transition: transform 0.3s ease;
}

.github-link:hover .github-icon {
  transform: rotate(360deg);
}

.github-text {
  font-family: 'Inter', sans-serif;
}

.hero-background {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 1;
}

/* ==================== X OBERON 水印系统 ==================== */
.watermark-text {
  position: absolute;
  font-size: clamp(8rem, 18vw, 22rem);
  font-weight: 900;
  color: rgba(0, 0, 0, 0.08);
  font-family: 'Inter', 'Helvetica', sans-serif;
  letter-spacing: 0.1em;
  user-select: none;
  pointer-events: none;
  white-space: nowrap;
}

.watermark-1 {
  top: 15%;
  left: -5%;
  transform: rotate(-12deg);
  animation: watermarkFloat1 8s ease-in-out infinite;
}

.watermark-2 {
  top: 50%;
  right: -8%;
  transform: rotate(8deg) translateY(-50%);
  animation: watermarkFloat2 10s ease-in-out infinite reverse;
  opacity: 0.06;
}

.watermark-3 {
  bottom: 15%;
  left: 25%;
  transform: rotate(-5deg);
  animation: watermarkFloat3 12s ease-in-out infinite;
  opacity: 0.04;
  font-size: clamp(6rem, 14vw, 18rem);
}

@keyframes watermarkFloat1 {
  0%, 100% {
    transform: rotate(-12deg) translateY(0px);
    opacity: 0.08;
  }
  50% {
    transform: rotate(-12deg) translateY(-15px);
    opacity: 0.12;
  }
}

@keyframes watermarkFloat2 {
  0%, 100% {
    transform: rotate(8deg) translateY(-50%) translateX(0px);
    opacity: 0.06;
  }
  50% {
    transform: rotate(8deg) translateY(-50%) translateX(10px);
    opacity: 0.09;
  }
}

@keyframes watermarkFloat3 {
  0%, 100% {
    transform: rotate(-5deg) scale(1);
    opacity: 0.04;
  }
  50% {
    transform: rotate(-5deg) scale(1.03);
    opacity: 0.07;
  }
}

.hero-content {
  position: relative;
  z-index: 2;
  text-align: center;
  max-width: 800px;
  padding: 0 20px;
}

.main-title {
  margin-bottom: 30px;
}

.title-line {
  display: block;
  font-size: clamp(3rem, 8vw, 8rem);
  font-weight: 900;
  line-height: 0.9;
  margin-bottom: 10px;
}

.title-line.highlight {
  color: #fff;
  text-shadow: 2px 2px 0px #000;
}

.hero-subtitle {
  margin-bottom: 50px;
}

.hero-subtitle p {
  font-size: 1.2rem;
  font-weight: 400;
  opacity: 0.8;
  letter-spacing: 1px;
}

.cta-button {
  background-color: #000;
  color: #f43a47;
  border: none;
  padding: 20px 40px;
  font-size: 1rem;
  font-weight: 600;
  letter-spacing: 2px;
  cursor: pointer;
  transition: all 0.3s ease;
  text-transform: uppercase;
}

.cta-button:hover {
  background-color: #fff;
  color: #000;
  transform: translateY(-2px);
}

.scroll-indicator {
  position: absolute;
  bottom: 50px;
  left: 50%;
  transform: translateX(-50%);
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 10px;
}

.scroll-line {
  width: 2px;
  height: 50px;
  background-color: #000;
  animation: scrollPulse 2s infinite;
}

.scroll-text {
  font-size: 0.8rem;
  font-weight: 500;
  letter-spacing: 2px;
  writing-mode: vertical-rl;
  text-orientation: mixed;
}

@keyframes scrollPulse {
  0%, 100% { opacity: 0.3; }
  50% { opacity: 1; }
}

/* ==================== 项目区域 ==================== */
.projects-section {
  padding: 100px 50px;
  background-color: #fff;
  color: #000;
}

.section-header {
  text-align: center;
  margin-bottom: 80px;
}

.section-title {
  font-size: 3rem;
  font-weight: 900;
  margin-bottom: 20px;
  letter-spacing: 2px;
}

.section-line {
  width: 100px;
  height: 3px;
  background-color: #f43a47;
  margin: 0 auto;
}

.projects-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
  gap: 50px;
  max-width: 1200px;
  margin: 0 auto;
}

.project-card {
  background-color: #f8f8f8;
  border-radius: 10px;
  overflow: hidden;
  transition: transform 0.3s ease, box-shadow 0.3s ease;
  cursor: pointer;
}

.project-card:hover {
  transform: translateY(-10px);
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
}

.project-image {
  height: 250px;
  background: linear-gradient(135deg, #f43a47, #ff6b7a);
  display: flex;
  align-items: center;
  justify-content: center;
}

.project-placeholder {
  color: #fff;
  font-size: 2rem;
  font-weight: 700;
  letter-spacing: 2px;
}

.project-info {
  padding: 30px;
}

.project-title {
  font-size: 1.5rem;
  font-weight: 700;
  margin-bottom: 15px;
}

.project-description {
  font-size: 1rem;
  line-height: 1.6;
  margin-bottom: 20px;
  opacity: 0.8;
}

.project-tech {
  display: flex;
  gap: 10px;
  flex-wrap: wrap;
}

.tech-tag {
  background-color: #f43a47;
  color: #fff;
  padding: 5px 15px;
  border-radius: 20px;
  font-size: 0.8rem;
  font-weight: 500;
}

/* ==================== 联系区域 ==================== */
.contact-section {
  padding: 100px 50px;
  background-color: #000;
  color: #fff;
  text-align: center;
}

.contact-title {
  font-size: 3rem;
  font-weight: 900;
  margin-bottom: 20px;
  letter-spacing: 2px;
}

.contact-subtitle {
  font-size: 1.2rem;
  margin-bottom: 50px;
  opacity: 0.8;
}

.contact-buttons {
  display: flex;
  gap: 30px;
  justify-content: center;
  flex-wrap: wrap;
}

.contact-button {
  display: inline-block;
  padding: 20px 40px;
  background-color: #f43a47;
  color: #fff;
  text-decoration: none;
  font-weight: 600;
  letter-spacing: 2px;
  text-transform: uppercase;
  transition: all 0.3s ease;
}

.contact-button:hover {
  background-color: #fff;
  color: #000;
  transform: translateY(-2px);
}

.contact-button.secondary {
  background-color: transparent;
  border: 2px solid #fff;
  color: #fff;
}

.contact-button.secondary:hover {
  background-color: #fff;
  color: #000;
}

/* ==================== 关于页面样式 ==================== */
.about-hero {
  position: relative;
  height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
  overflow: hidden;
}

.about-content {
  position: relative;
  z-index: 2;
  text-align: center;
  max-width: 800px;
  padding: 0 20px;
}

.about-title {
  margin-bottom: 40px;
}

.about-description {
  font-size: 1.3rem;
  line-height: 1.8;
  font-weight: 400;
  opacity: 0.9;
  max-width: 600px;
  margin: 0 auto;
}

/* ==================== 技能区域 ==================== */
.skills-section {
  padding: 100px 50px;
  background-color: #fff;
  color: #000;
}

.skills-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 50px;
  max-width: 1200px;
  margin: 0 auto;
}

.skill-category {
  background-color: #f8f8f8;
  padding: 40px;
  border-radius: 10px;
}

.skill-title {
  font-size: 1.5rem;
  font-weight: 700;
  margin-bottom: 30px;
  color: #f43a47;
}

.skill-list {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.skill-item {
  display: flex;
  flex-direction: column;
  gap: 10px;
}

.skill-name {
  font-weight: 600;
  font-size: 1rem;
}

.skill-bar {
  width: 100%;
  height: 8px;
  background-color: #e0e0e0;
  border-radius: 4px;
  overflow: hidden;
}

.skill-progress {
  height: 100%;
  background: linear-gradient(90deg, #f43a47, #ff6b7a);
  width: 0%;
  transition: width 1s ease-in-out;
  border-radius: 4px;
}

/* ==================== 时间线样式 ==================== */
.timeline-section {
  padding: 100px 50px;
  background-color: #f8f8f8;
  color: #000;
}

.timeline {
  max-width: 800px;
  margin: 0 auto;
  position: relative;
}

.timeline::before {
  content: '';
  position: absolute;
  left: 50%;
  top: 0;
  bottom: 0;
  width: 2px;
  background-color: #f43a47;
  transform: translateX(-50%);
}

.timeline-item {
  display: flex;
  margin-bottom: 50px;
  position: relative;
}

.timeline-item:nth-child(odd) {
  flex-direction: row;
}

.timeline-item:nth-child(even) {
  flex-direction: row-reverse;
}

.timeline-date {
  flex: 0 0 150px;
  text-align: center;
  font-weight: 700;
  color: #f43a47;
  font-size: 1.1rem;
  display: flex;
  align-items: center;
  justify-content: center;
}

.timeline-content {
  flex: 1;
  background-color: #fff;
  padding: 30px;
  border-radius: 10px;
  margin: 0 30px;
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
  position: relative;
}

.timeline-item:nth-child(odd) .timeline-content::before {
  content: '';
  position: absolute;
  left: -10px;
  top: 50%;
  transform: translateY(-50%);
  width: 0;
  height: 0;
  border-top: 10px solid transparent;
  border-bottom: 10px solid transparent;
  border-right: 10px solid #fff;
}

.timeline-item:nth-child(even) .timeline-content::before {
  content: '';
  position: absolute;
  right: -10px;
  top: 50%;
  transform: translateY(-50%);
  width: 0;
  height: 0;
  border-top: 10px solid transparent;
  border-bottom: 10px solid transparent;
  border-left: 10px solid #fff;
}

.timeline-title {
  font-size: 1.3rem;
  font-weight: 700;
  margin-bottom: 10px;
  color: #000;
}

.timeline-company {
  font-size: 1rem;
  font-weight: 600;
  color: #f43a47;
  margin-bottom: 15px;
}

.timeline-description {
  font-size: 1rem;
  line-height: 1.6;
  opacity: 0.8;
}

/* ==================== 响应式设计 ==================== */
@media (max-width: 768px) {
  .navbar {
    padding: 20px 30px;
  }

  .nav-links {
    gap: 20px;
  }

  .hero-content,
  .about-content {
    padding: 0 30px;
  }

  .title-line {
    font-size: clamp(2rem, 10vw, 4rem);
  }

  /* 移动端右侧导航优化 */
  .page-navigation {
    right: 15px;
    gap: 10px;
  }

  .nav-dot {
    width: 10px;
    height: 10px;
  }

  .nav-dot::before {
    display: none; /* 移动端隐藏标签 */
  }

  .scroll-hint {
    right: 15px;
    bottom: 20px;
  }

  .scroll-arrow {
    width: 16px;
    height: 16px;
  }

  .projects-section,
  .skills-section,
  .timeline-section,
  .contact-section {
    padding: 80px 30px;
  }

  .projects-grid {
    grid-template-columns: 1fr;
    gap: 30px;
  }

  .skills-grid {
    grid-template-columns: 1fr;
    gap: 30px;
  }

  .contact-buttons {
    flex-direction: column;
    align-items: center;
  }

  .timeline::before {
    left: 30px;
  }

  .timeline-item {
    flex-direction: column !important;
    margin-left: 60px;
  }

  .timeline-date {
    position: absolute;
    left: -90px;
    top: 0;
    width: 80px;
    font-size: 0.9rem;
  }

  .timeline-content {
    margin: 0;
  }

  .timeline-content::before {
    left: -10px !important;
    right: auto !important;
    border-right: 10px solid #fff !important;
    border-left: none !important;
  }

  /* 相机取景框移动端优化 */
  .frame-corner {
    width: 30px;
    height: 30px;
    border-width: 2px;
  }

  .frame-corner.top-left,
  .frame-corner.top-right {
    top: 15px;
  }

  .frame-corner.bottom-left,
  .frame-corner.bottom-right {
    bottom: 15px;
  }

  .frame-corner.top-left,
  .frame-corner.bottom-left {
    left: 15px;
  }

  .frame-corner.top-right,
  .frame-corner.bottom-right {
    right: 15px;
  }
}

/* ==================== 第2页 - 关于页面 ==================== */
#page2 {
  background-color: #fff;
  color: #000;
  display: flex;
  flex-direction: row;
  align-items: center;
  padding: 0;
  position: relative;
  overflow: hidden;
}

/* 左半部分 - 文字内容 */
.about-left-section {
  width: 50%;
  height: 100vh;
  padding: 4rem 3rem;
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 10;
  position: relative;
}

.about-content {
  max-width: 500px;
  text-align: left;
}

/* 右半部分 - 贴纸装饰 */
.about-right-section {
  width: 50%;
  height: 100vh;
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: #fff;
}

/* 简化的关于内容样式 */
.about-title {
  font-size: clamp(3rem, 8vw, 8rem);
  font-weight: 900;
  margin-bottom: 2rem;
  color: #000;
  letter-spacing: 3px;
  text-transform: uppercase;
}

.about-line {
  font-size: clamp(1.2rem, 2.5vw, 1.8rem);
  line-height: 1.6;
  margin-bottom: 2rem;
}

.about-line .normal {
  color: #666;
  font-weight: 400;
}

.about-line .highlight {
  color: #f43a47;
  font-weight: 700;
  margin: 0 0.5rem;
}

.about-paragraph {
  font-size: clamp(1rem, 2vw, 1.3rem);
  line-height: 1.8;
  color: #555;
  max-width: 500px;
}

/* 胶带效果 - 贴在左上角，带锯齿边缘 */
.sticker-group .sticker-pin {
  position: absolute;
  top: -6px;
  left: -6px;
  transform: rotate(-15deg);
  width: 32px;
  height: 14px;
  background: linear-gradient(135deg,
    rgba(255, 255, 255, 0.95) 0%,
    rgba(250, 250, 250, 0.9) 50%,
    rgba(245, 245, 245, 0.85) 100%);
  box-shadow:
    0 2px 4px rgba(0, 0, 0, 0.12),
    0 1px 2px rgba(0, 0, 0, 0.08),
    inset 0 1px 0 rgba(255, 255, 255, 0.7),
    inset 0 -1px 0 rgba(0, 0, 0, 0.06);
  z-index: 10;
  border-top: 1px solid rgba(230, 230, 230, 0.4);
  border-bottom: 1px solid rgba(230, 230, 230, 0.4);
  /* 使用clip-path创建锯齿边缘 */
  clip-path: polygon(
    0% 0%,
    3px 0%,
    5px 3px,
    7px 0%,
    10px 3px,
    12px 0%,
    15px 3px,
    17px 0%,
    20px 3px,
    22px 0%,
    25px 3px,
    27px 0%,
    30px 3px,
    32px 0%,
    32px 100%,
    30px 100%,
    27px 11px,
    25px 100%,
    22px 11px,
    20px 100%,
    17px 11px,
    15px 100%,
    12px 11px,
    10px 100%,
    7px 11px,
    5px 100%,
    3px 11px,
    0% 100%
  );
}

.sticker-group .sticker-pin::before {
  content: '';
  position: absolute;
  top: 2px;
  left: 3px;
  right: 3px;
  height: 1px;
  background: linear-gradient(90deg,
    transparent 0%,
    rgba(255, 255, 255, 0.8) 20%,
    rgba(255, 255, 255, 0.5) 80%,
    transparent 100%);
  /* 高光也需要跟随锯齿形状 */
  clip-path: polygon(
    0% 0%,
    10% 0%,
    15% 100%,
    25% 0%,
    35% 100%,
    45% 0%,
    55% 100%,
    65% 0%,
    75% 100%,
    85% 0%,
    95% 100%,
    100% 0%,
    100% 100%,
    0% 100%
  );
}

.sticker-group .sticker-pin::after {
  content: '';
  position: absolute;
  bottom: 2px;
  left: 3px;
  right: 3px;
  height: 1px;
  background: linear-gradient(90deg,
    transparent 0%,
    rgba(0, 0, 0, 0.06) 30%,
    rgba(0, 0, 0, 0.03) 70%,
    transparent 100%);
}

/* 响应式设计 */
@media (max-width: 768px) {
  #page2 {
    flex-direction: column;
  }

  .about-left-section,
  .about-right-section {
    width: 100%;
    height: 50vh;
    padding: 2rem 1.5rem;
  }

  .about-content {
    max-width: 100%;
  }

  .about-title {
    font-size: 2rem;
    margin-bottom: 1.5rem;
  }

  .about-line {
    font-size: 1rem;
  }

  .about-paragraph {
    font-size: 0.9rem;
  }
}

@media (max-width: 1024px) and (min-width: 769px) {
  .about-left-section {
    padding: 3rem 2rem;
  }

  .about-content {
    max-width: 400px;
  }
}

/* ==================== 贴纸特效系统 ==================== */
.sticker-decorations {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  pointer-events: none;
  z-index: 5;
}

.sticker-group {
  position: absolute;
}

.sticker-group-1 {
  top: 8%;
  left: 8%;
  transform: rotate(-10deg);
}

.sticker-group-2 {
  top: 15%;
  right: 12%;
  transform: rotate(8deg);
}

.sticker-group-3 {
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%) rotate(-3deg);
}

.sticker-group-4 {
  bottom: 25%;
  left: 15%;
  transform: rotate(-12deg);
}

.sticker-group-5 {
  bottom: 15%;
  right: 15%;
  transform: rotate(12deg);
}

.sticker {
  position: relative;
  margin: 10px;
  filter: drop-shadow(2px 4px 8px rgba(0, 0, 0, 0.15));
  transition: all 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);
  cursor: pointer;
}

.sticker:hover {
  transform: scale(1.1);
  filter: drop-shadow(3px 6px 12px rgba(0, 0, 0, 0.25));
}

/* 拍立得风格贴纸 */
.sticker-polaroid {
  background: #fff;
  border: 10px solid #fff;
  border-bottom: 25px solid #fff;
  width: 100px;
  height: 115px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
}

.sticker-polaroid.sticker-rotated {
  transform: rotate(-15deg);
}

.sticker-polaroid.sticker-tilted {
  transform: rotate(12deg);
}

/* 更大尺寸的贴纸 */
.sticker-polaroid.sticker-xl {
  width: 130px;
  height: 155px;
  border: 12px solid #fff;
  border-bottom: 30px solid #fff;
}

.sticker-polaroid.sticker-xxl {
  width: 160px;
  height: 190px;
  border: 15px solid #fff;
  border-bottom: 35px solid #fff;
}

.sticker-tag.sticker-xl {
  padding: 15px 30px;
  font-size: 18px;
  border-radius: 35px;
  font-weight: 800;
}

.sticker-badge.sticker-xl {
  padding: 18px 35px;
  font-size: 16px;
  border-radius: 40px;
  font-weight: 800;
}

/* 大尺寸贴纸的字体调整 */
.sticker-xl .sticker-image {
  font-size: 20px;
  font-weight: 900;
}

.sticker-xl .sticker-label {
  font-size: 12px;
  font-weight: 700;
}

.sticker-xxl .sticker-image {
  font-size: 26px;
  font-weight: 900;
}

.sticker-xxl .sticker-label {
  font-size: 14px;
  font-weight: 700;
}

.sticker-content {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
}

.sticker-image {
  background: #f43a47;
  color: #fff;
  width: 100%;
  height: 60%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: 900;
  font-size: 16px;
  letter-spacing: 1px;
}

.sticker-label {
  color: #000;
  font-size: 10px;
  font-weight: 600;
  margin-top: 4px;
  letter-spacing: 0.5px;
}

/* 标签风格贴纸 */
.sticker-tag {
  background: #f43a47;
  color: #fff;
  padding: 10px 20px;
  border-radius: 25px;
  font-size: 14px;
  font-weight: 700;
  letter-spacing: 1px;
  box-shadow: 0 2px 6px rgba(244, 58, 71, 0.3);
}

.sticker-tag.sticker-small {
  padding: 6px 12px;
  font-size: 10px;
}

.sticker-tag .sticker-text {
  text-transform: uppercase;
}

/* 徽章风格贴纸 */
.sticker-badge {
  background: #000;
  color: #fff;
  padding: 12px 22px;
  border-radius: 30px;
  font-size: 13px;
  font-weight: 800;
  letter-spacing: 1.5px;
  border: 2px solid #f43a47;
  box-shadow: 0 3px 10px rgba(0, 0, 0, 0.2);
}

.sticker-badge .sticker-text {
  text-transform: uppercase;
}

/* 贴纸动画效果 */
@keyframes stickerFloat {
  0%, 100% { transform: translateY(0px) rotate(0deg); }
  50% { transform: translateY(-5px) rotate(1deg); }
}

.sticker-group-1 .sticker {
  animation: stickerFloat 6s ease-in-out infinite;
  animation-delay: 0s;
}

.sticker-group-2 .sticker {
  animation: stickerFloat 7s ease-in-out infinite;
  animation-delay: 1s;
}

.sticker-group-3 .sticker {
  animation: stickerFloat 5s ease-in-out infinite;
  animation-delay: 2s;
}

.sticker-group-4 .sticker {
  animation: stickerFloat 8s ease-in-out infinite;
  animation-delay: 0.5s;
}

.about-title {
  font-size: clamp(3rem, 8vw, 8rem);
  font-weight: 900;
  margin-bottom: 50px;
  color: #f43a47;
}

.about-line {
  font-size: 1.5rem;
  line-height: 1.6;
  margin-bottom: 30px;
}

.normal {
  color: #000;
  font-weight: 400;
}

.highlight {
  color: #f43a47;
  font-weight: 600;
  margin: 0 10px;
}

.about-paragraph {
  font-size: 1.2rem;
  line-height: 1.8;
  font-weight: 300;
  opacity: 0.8;
}

/* ==================== 简单装饰元素 ==================== */
.page-decoration {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  pointer-events: none;
  z-index: 1;
}

.deco-dot {
  position: absolute;
  width: 8px;
  height: 8px;
  background-color: #f43a47;
  border-radius: 50%;
  opacity: 0.3;
  animation: pulse 3s ease-in-out infinite;
}

.dot-1 {
  top: 20%;
  left: 10%;
  animation-delay: 0s;
}

.dot-2 {
  bottom: 30%;
  right: 15%;
  animation-delay: 1s;
}

.deco-line {
  position: absolute;
  background-color: #f43a47;
  opacity: 0.2;
}

.line-1 {
  width: 2px;
  height: 100px;
  top: 15%;
  right: 8%;
  animation: slideUp 4s ease-in-out infinite;
}

.floating-dot {
  position: absolute;
  width: 6px;
  height: 6px;
  background-color: #f43a47;
  border-radius: 50%;
  opacity: 0.4;
  animation: float 5s ease-in-out infinite;
}

.floating-dot.dot-1 {
  top: 25%;
  left: 15%;
  animation-delay: 0s;
}

.floating-dot.dot-2 {
  top: 60%;
  right: 20%;
  animation-delay: 1.5s;
}

.floating-dot.dot-3 {
  bottom: 25%;
  left: 25%;
  animation-delay: 3s;
}

.deco-circle {
  position: absolute;
  width: 40px;
  height: 40px;
  border: 2px solid #000;
  border-radius: 50%;
  opacity: 0.1;
  top: 30%;
  right: 10%;
  animation: rotate 8s linear infinite;
}

.deco-square {
  position: absolute;
  width: 30px;
  height: 30px;
  background-color: #000;
  opacity: 0.1;
  bottom: 20%;
  left: 8%;
  animation: pulse 4s ease-in-out infinite;
}

.code-symbol {
  position: absolute;
  font-size: 2rem;
  font-weight: 700;
  color: #f43a47;
  opacity: 0.4;
  font-family: 'Monaco', monospace;
  text-shadow: 0 0 10px rgba(244, 58, 71, 0.3);
  transition: all 0.3s ease;
}

.code-symbol:hover {
  opacity: 0.8;
  text-shadow: 0 0 20px rgba(244, 58, 71, 0.6);
  transform: scale(1.1);
}

.symbol-1 {
  top: 20%;
  left: 10%;
  animation: float 6s ease-in-out infinite;
}

.symbol-2 {
  top: 60%;
  right: 15%;
  animation: float 6s ease-in-out infinite;
  animation-delay: 2s;
}

.symbol-3 {
  bottom: 25%;
  left: 20%;
  animation: float 6s ease-in-out infinite;
  animation-delay: 4s;
}

/* ==================== 贴纸特效移动端响应式 ==================== */
@media (max-width: 768px) {
  .sticker-decorations {
    display: none; /* 在移动端隐藏贴纸以保持简洁 */
  }
}

@media (max-width: 1024px) and (min-width: 769px) {
  .sticker-group-1 {
    top: 5%;
    left: 3%;
  }

  .sticker-group-2 {
    top: 8%;
    right: 5%;
  }

  .sticker-group-3 {
    bottom: 12%;
    left: 4%;
  }

  .sticker-group-4 {
    bottom: 15%;
    right: 7%;
  }

  .sticker-group-5 {
    bottom: 12%;
    right: 12%;
  }

  .sticker-polaroid {
    width: 85px;
    height: 100px;
    border: 8px solid #fff;
    border-bottom: 20px solid #fff;
  }

  .sticker-image {
    font-size: 14px;
  }

  .sticker-label {
    font-size: 9px;
  }

  .sticker-tag {
    padding: 8px 16px;
    font-size: 12px;
  }

  .sticker-badge {
    padding: 10px 18px;
    font-size: 11px;
  }

  /* 大尺寸贴纸响应式 */
  .sticker-polaroid.sticker-xl {
    width: 110px;
    height: 130px;
    border: 10px solid #fff;
    border-bottom: 25px solid #fff;
  }

  .sticker-polaroid.sticker-xxl {
    width: 130px;
    height: 155px;
    border: 12px solid #fff;
    border-bottom: 30px solid #fff;
  }

  .sticker-tag.sticker-xl {
    padding: 12px 24px;
    font-size: 15px;
  }

  .sticker-badge.sticker-xl {
    padding: 14px 28px;
    font-size: 13px;
  }
}

@keyframes pulse {
  0%, 100% { opacity: 0.2; transform: scale(1); }
  50% { opacity: 0.5; transform: scale(1.1); }
}

@keyframes float {
  0%, 100% { transform: translateY(0px); }
  50% { transform: translateY(-15px); }
}

@keyframes slideUp {
  0%, 100% { transform: translateY(0); opacity: 0.2; }
  50% { transform: translateY(-20px); opacity: 0.4; }
}

@keyframes rotate {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}

/* ==================== 第3页 - 水波游泳池效果 (主体配色) ==================== */
#page3 {
  background: linear-gradient(135deg, #f43a47 0%, #d32f3c 50%, #f43a47 100%);
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
  overflow: hidden;
}

/* 水波容器 */
.water-container {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 5;
}

.water-canvas {
  width: 100%;
  height: 100%;
  display: block;
  cursor: crosshair;
}

/* 水波上的标题内容 */
.water-overlay-content {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  z-index: 10;
  text-align: center;
  color: white;
  pointer-events: none;
}

.water-title {
  margin-bottom: 2rem;
}

.water-main-title {
  font-size: clamp(3rem, 8vw, 8rem);
  font-weight: 900;
  line-height: 0.9;
  letter-spacing: -0.02em;
  margin: 0;
  text-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);
}

.water-title-line {
  display: block;
  opacity: 0;
  transform: translateY(100px);
  animation: waterTitleSlideUp 1s ease-out forwards;
}

.water-title-line:nth-child(1) {
  animation-delay: 0.2s;
}

.water-title-line:nth-child(2) {
  animation-delay: 0.4s;
}

.water-title-line:nth-child(3) {
  animation-delay: 0.6s;
}

.water-highlight {
  background: linear-gradient(135deg, #f43a47 0%, #ff6b6b 50%, #ffa726 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  position: relative;
}

.water-subtitle {
  font-size: clamp(1rem, 2vw, 1.5rem);
  font-weight: 300;
  opacity: 0;
  transform: translateY(30px);
  animation: waterSubtitleFadeIn 1s ease-out 0.8s forwards;
  text-shadow: 0 2px 10px rgba(0, 0, 0, 0.3);
}

.water-subtitle p {
  margin: 0;
  letter-spacing: 0.1em;
}

/* 水波标题动画 */
@keyframes waterTitleSlideUp {
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes waterSubtitleFadeIn {
  to {
    opacity: 0.9;
    transform: translateY(0);
  }
}







/* 响应式设计 */
@media (max-width: 1024px) {
  .identity-container {
    grid-template-columns: 1fr;
    gap: 4rem;
    text-align: center;
  }

  .visual-element {
    width: 300px;
    height: 300px;
  }

  .circle-large {
    width: 250px;
    height: 250px;
    top: 25px;
    left: 25px;
  }

  .circle-medium {
    width: 150px;
    height: 150px;
    top: 75px;
    left: 75px;
  }

  .circle-small {
    width: 80px;
    height: 80px;
    top: 110px;
    left: 110px;
  }

  .line-element {
    height: 150px;
    top: 75px;
    left: 150px;
  }
}

@media (max-width: 768px) {
  #page3 {
    padding: 0 3vw;
  }

  .identity-main-title {
    font-size: clamp(2.5rem, 10vw, 5rem);
  }

  .identity-skills {
    justify-content: center;
  }

  .visual-element {
    width: 250px;
    height: 250px;
  }
}

/* ==================== 第4页 - 创作者 ==================== */
#page4 {
  background-color: #fff;
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
  overflow: hidden;
}



/* ==================== 第3页文字颜色调整 ==================== */
#page3 .title-line {
  color: #000;
}

#page3 .title-line.highlight {
  color: #fff;
  text-shadow: 2px 2px 0px #000;
}

#page3 .hero-subtitle p {
  color: rgba(0, 0, 0, 0.8);
}

/* ==================== 第4页文字颜色调整 ==================== */
#page4 .title-line {
  color: #000;
}

#page4 .title-line.highlight {
  color: #f43a47;
  text-shadow: 2px 2px 0px #fff;
}

#page4 .hero-subtitle p {
  color: rgba(0, 0, 0, 0.8);
}

/* ==================== 第5页 - 纯红色背景 ==================== */
#page5 {
  background-color: #f43a47;
}

/* ==================== 动画效果 ==================== */
@keyframes particleFloat {
  0%, 100% {
    transform: translateY(0px) translateX(0px);
    opacity: 0.2;
  }
  25% {
    transform: translateY(-20px) translateX(10px);
    opacity: 0.5;
  }
  50% {
    transform: translateY(-10px) translateX(-15px);
    opacity: 0.8;
  }
  75% {
    transform: translateY(-30px) translateX(5px);
    opacity: 0.3;
  }
  max-width: 160px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  z-index: 5;
}

.stacked-card:hover .card-tab-label {
  opacity: 0.9;
  border-color: #3b82f6;
}

.card-number {
  color: #1f2937; /* text-gray-800 */
  font-weight: 700; /* font-bold */
}

.card-title {
  margin-left: 12px; /* ml-3 */
  color: #6b7280; /* text-gray-600 */
}

/* 分类指示器 - 完全按照您的React代码 */
.category-indicator {
  position: absolute;
  top: 16px; /* top-4 */
  left: 16px; /* left-4 */
  width: 32px; /* w-8 */
  height: 32px; /* h-8 */
  background: #000; /* bg-black */
  color: #fff; /* text-white */
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 4px; /* rounded */
  font-size: 0.875rem; /* text-sm */
  font-weight: 700; /* font-bold */
  transition: all 0.3s ease;
  transform: scale(1);
}

.stacked-card.hovered .category-indicator {
  transform: scale(1.1); /* scale-110 */
}

/* 卡片内容 */
.card-content {
  padding: 20px;
  height: 100%;
  display: flex;
  flex-direction: column;
  justify-content: center;
  flex: 1;
}

.card-header {
  text-align: center;
}

.card-title-main {
  font-size: 1.125rem;
  font-weight: 700;
  color: #1f2937;
  margin-bottom: 8px;
  line-height: 1.2;
}

.card-subtitle {
  font-size: 0.875rem;
  color: #6b7280;
  line-height: 1.3;
}



/* 详细信息 - 悬停时显示 */
.card-details {
  transition: all 0.3s ease;
  overflow: hidden;
  max-height: 0;
  opacity: 0;
}

.stacked-card.hovered .card-details {
  max-height: 80px; /* max-h-20 */
  opacity: 1;
}

.details-border {
  padding-top: 16px; /* pt-4 */
  border-top: 1px solid #e5e7eb; /* border-gray-200 */
}

.detail-text {
  font-size: 0.75rem; /* text-xs */
  color: #6b7280; /* text-gray-600 */
}

.detail-description {
  font-size: 0.875rem; /* text-sm */
  color: #374151; /* text-gray-700 */
  margin-top: 4px; /* mt-1 */
}

/* 响应式设计 */
@media (max-width: 1024px) {
  .stacked-cards-container {
    max-width: 900px;
    height: 500px;
  }

  .stacked-card {
    width: 280px;
    height: 200px;
  }

  .stacked-card.hovered {
    width: 350px;
    height: 240px;
  }

  .page-title {
    font-size: 2.5rem;
  }
}

@media (max-width: 768px) {
  #page5 {
    padding: 1rem;
  }

  .stacked-cards-container {
    max-width: 100%;
    height: 450px;
  }

  .stacked-card {
    width: 240px;
    height: 180px;
  }

  .stacked-card.hovered {
    width: 300px;
    height: 220px;
  }

  /* 调整移动端卡片位置间距 */
  .stacked-card[data-index="0"] { left: 0px; top: 0px; }
  .stacked-card[data-index="1"] { left: 35px; top: 20px; }
  .stacked-card[data-index="2"] { left: 70px; top: 40px; }
  .stacked-card[data-index="3"] { left: 105px; top: 60px; }
  .stacked-card[data-index="4"] { left: 140px; top: 80px; }
  .stacked-card[data-index="5"] { left: 175px; top: 100px; }

  .page-title {
    font-size: 2rem;
    margin-bottom: 1rem;
  }

  .page-subtitle {
    font-size: 0.9rem;
    margin-bottom: 2rem;
  }

  .card-content {
    padding: 16px;
  }

  .card-title-main {
    font-size: 1.1rem;
  }

  .card-image {
    font-size: 1.5rem;
  }

  .stacked-card.hovered .card-image {
    font-size: 2rem;
  }
}

/* 动画效果 */
@keyframes cardFadeIn {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* 卡片入场动画 */
.stacked-card {
  opacity: 0;
  animation: cardFadeIn 0.6s ease-out forwards;
}

.stacked-card[data-index="0"] { animation-delay: 0.1s; }
.stacked-card[data-index="1"] { animation-delay: 0.15s; }
.stacked-card[data-index="2"] { animation-delay: 0.2s; }
.stacked-card[data-index="3"] { animation-delay: 0.25s; }
.stacked-card[data-index="4"] { animation-delay: 0.3s; }
.stacked-card[data-index="5"] { animation-delay: 0.35s; }

/* 页面标题入场动画 */
.page-title {
  opacity: 0;
  transform: translateY(-20px);
  animation: titleFadeIn 0.8s ease-out 0.2s forwards;
}

.page-subtitle {
  opacity: 0;
  transform: translateY(-15px);
  animation: titleFadeIn 0.8s ease-out 0.4s forwards;
}

@keyframes titleFadeIn {
  to {
    opacity: 1;
    transform: translateY(0);
  }
}













/* 卡片标签页 */
.card-tab {
  background: #f43a47;
  color: #fff;
  padding: 1rem 1.5rem;
  display: flex;
  align-items: center;
  gap: 1rem;
  font-weight: 600;
  position: relative;
  border-radius: 20px 20px 0 0;
}

.card-number {
  background: rgba(0, 0, 0, 0.2);
  padding: 0.3rem 0.8rem;
  border-radius: 20px;
  font-size: 0.9rem;
  font-weight: 700;
}

.card-title {
  font-size: 1.1rem;
  letter-spacing: 0.5px;
  text-transform: lowercase;
}

/* 卡片内容 */
.card-content {
  padding: 2rem 1.5rem;
  text-align: center;
  height: calc(100% - 80px);
  display: flex;
  flex-direction: column;
  justify-content: space-between;
}

.card-icon {
  font-size: 3rem;
  margin-bottom: 1rem;
  display: block;
}

.card-content h3 {
  font-size: 1.4rem;
  font-weight: 700;
  color: #333;
  margin-bottom: 1rem;
  line-height: 1.3;
}

.card-content p {
  font-size: 0.95rem;
  color: #666;
  line-height: 1.6;
  margin-bottom: 1.5rem;
  flex-grow: 1;
}

/* 技术标签 */
.tech-stack {
  display: flex;
  flex-wrap: wrap;
  gap: 0.5rem;
  justify-content: center;
  margin-bottom: 1.5rem;
}

.tech-tag {
  background: #f43a47;
  color: #fff;
  padding: 0.4rem 0.8rem;
  border-radius: 20px;
  font-size: 0.8rem;
  font-weight: 500;
  transition: all 0.3s ease;
}

.tech-tag:hover {
  background: #d32f3c;
  transform: scale(1.1);
}

/* 查看项目按钮 */
.view-project-btn {
  background: #f43a47;
  color: #fff;
  border: none;
  padding: 0.8rem 2rem;
  border-radius: 25px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  text-transform: uppercase;
  letter-spacing: 1px;
  font-size: 0.9rem;
}

.view-project-btn:hover {
  background: #d32f3c;
  transform: translateY(-2px);
  box-shadow: 0 5px 15px rgba(244, 58, 71, 0.4);
}

/* 卡片悬停时的额外效果 */
.stacked-card:hover .card-tab {
  background: #d32f3c;
}

.stacked-card:hover .card-icon {
  animation: bounce 0.6s ease-in-out;
}

.stacked-card:hover .tech-tag {
  animation: pulse 0.8s ease-in-out infinite;
}











/* ==================== 动画效果 ==================== */
@keyframes particleFloat {
  0%, 100% {
    transform: translateY(0px) translateX(0px);
    opacity: 0.2;
  }
  25% {
    transform: translateY(-20px) translateX(10px);
    opacity: 0.5;
  }
  50% {
    transform: translateY(-10px) translateX(-15px);
    opacity: 0.8;
  }
  75% {
    transform: translateY(-30px) translateX(5px);
    opacity: 0.3;
  }
}



.developer-link:hover .link-arrow {
  transform: translateX(5px);
}

/* ==================== 右侧详情面板样式 ==================== */

/* 右侧面板 */
.cards-right-panel {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
}

/* 详情面板容器 */
.detail-panel {
  width: 100%;
  height: 600px;
  background: rgba(255, 255, 255, 0.95);
  border-radius: 12px;
  box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
  backdrop-filter: blur(10px);
  padding: 1.5rem;
  display: flex;
  align-items: center;
  justify-content: center;
  overflow: hidden;
}

/* 默认占位符 */
.detail-placeholder {
  text-align: center;
  color: #6b7280;
}

.placeholder-icon {
  margin: 0 auto 1.5rem;
  color: #9ca3af;
}

.detail-placeholder h3 {
  font-size: 1.5rem;
  font-weight: 600;
  color: #374151;
  margin-bottom: 0.5rem;
}

.detail-placeholder p {
  color: #6b7280;
  font-size: 1rem;
}

/* 详情内容 */
.detail-content {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

/* 详情头部 */
.detail-header {
  display: flex;
  align-items: flex-start;
  gap: 1rem;
  margin-bottom: 1.5rem;
  flex-shrink: 0;
}

.detail-category-indicator {
  width: 48px;
  height: 48px;
  background: #000;
  color: #fff;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 8px;
  font-size: 1.125rem;
  font-weight: 700;
  flex-shrink: 0;
}

.detail-title-section {
  flex: 1;
}

.detail-title {
  font-size: 2rem;
  font-weight: 700;
  color: #1f2937;
  margin-bottom: 0.25rem;
}

.detail-subtitle {
  color: #6b7280;
  font-size: 1rem;
  margin-bottom: 0.5rem;
}

.detail-number {
  font-size: 0.875rem;
  color: #9ca3af;
}

/* 详情主要内容 */
.detail-main {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 1rem;
  overflow-y: auto;
  padding-right: 0.5rem;
}

/* 详情色卡 */
.detail-color-card-container {
  display: flex;
  justify-content: center;
  margin-bottom: 1rem;
  flex-shrink: 0;
}

.detail-color-card {
  width: 200px;
  height: 150px;
  border-radius: 12px;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 1.5rem;
  font-weight: 700;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
  transition: all 0.3s ease;
}

/* 不同类别的色卡颜色 */
.detail-color-card.category-s {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.detail-color-card.category-o {
  background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
}

.detail-color-card.category-p {
  background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
}

/* 描述部分 */
.detail-description {
  flex-shrink: 0;
}

.detail-description h3 {
  font-size: 1rem;
  font-weight: 600;
  color: #1f2937;
  margin-bottom: 0.5rem;
}

.detail-description p {
  color: #4b5563;
  line-height: 1.5;
  font-size: 0.875rem;
  text-align: justify;
  word-wrap: break-word;
  overflow-wrap: break-word;
}

/* 规格部分 */
.detail-specs {
  flex-shrink: 0;
}

.detail-specs h3 {
  font-size: 1rem;
  font-weight: 600;
  color: #1f2937;
  margin-bottom: 0.5rem;
}

.specs-list {
  display: flex;
  flex-direction: column;
  gap: 0.25rem;
}

.spec-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0.375rem 0;
  border-bottom: 1px solid #f3f4f6;
}

.spec-item:last-child {
  border-bottom: none;
}

.spec-label {
  color: #6b7280;
  font-size: 0.75rem;
  flex-shrink: 0;
}

.spec-value {
  font-weight: 500;
  color: #1f2937;
  font-size: 0.75rem;
  text-align: right;
  word-wrap: break-word;
  overflow-wrap: break-word;
}

/* ==================== 项目阶段管理样式 ==================== */
#page5 {
  background-color: #f43a47;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 2rem;
}

.project-management-container {
  max-width: 1200px;
  margin: 0 auto;
  width: 100%;
}

.project-header {
  margin-bottom: 2rem;
  text-align: center;
}

.project-header h1 {
  font-size: 2rem;
  font-weight: bold;
  color: #ffffff;
  margin-bottom: 0.5rem;
}

.project-header p {
  color: #ffffff;
  opacity: 0.9;
  font-size: 1rem;
}

.cards-container {
  position: relative;
  min-height: 500px;
  display: flex;
  justify-content: flex-start;
  align-items: flex-start;
}

.card {
  position: absolute;
  width: 100%;
  max-width: 450px;
  background: white;
  border-radius: 0.5rem;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
  border: 2px solid #e7e5e4;
  overflow: hidden;
  transition: all 0.7s cubic-bezier(0.4, 0, 0.2, 1);
}

.card.selected {
  transform: translateX(120%) scale(1.05);
  width: 480px;
  max-width: 480px;
  z-index: 100 !important;
}

.card-header {
  padding: 1.25rem;
  cursor: pointer;
  position: relative;
  overflow: hidden;
  border-bottom: 2px solid #e7e5e4;
  transition: all 0.2s ease;
  clip-path: polygon(
    0 12px,
    24px 0,
    48px 12px,
    72px 0,
    96px 12px,
    120px 0,
    144px 12px,
    168px 0,
    192px 12px,
    216px 0,
    240px 12px,
    264px 0,
    288px 12px,
    312px 0,
    336px 12px,
    360px 0,
    384px 12px,
    408px 0,
    432px 12px,
    456px 0,
    480px 12px,
    504px 0,
    528px 12px,
    552px 0,
    576px 12px,
    600px 0,
    624px 12px,
    648px 0,
    672px 12px,
    696px 0,
    720px 12px,
    744px 0,
    768px 12px,
    792px 0,
    816px 12px,
    100% 0,
    100% 100%,
    0 100%
  );
}

.card-header:hover {
  filter: brightness(1.1);
  transform: scale(1.02);
}

.card-header:active {
  transform: scale(0.98);
}

.card.selected .card-header {
  box-shadow: 0 10px 25px -5px rgba(0, 0, 0, 0.1);
}

.card-title {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.stage-name {
  font-weight: bold;
  font-size: 1.25rem;
  letter-spacing: 0.05em;
}

.stage-title {
  font-weight: 600;
  font-size: 1.125rem;
  text-align: right;
}

.indicator {
  position: absolute;
  top: 0.5rem;
  right: 0.5rem;
  width: 0.5rem;
  height: 0.5rem;
  background-color: currentColor;
  border-radius: 50%;
  opacity: 0.6;
  animation: pulse 2s infinite;
}

@keyframes pulse {
  0%,
  100% {
    opacity: 0.6;
  }
  50% {
    opacity: 1;
  }
}

.close-btn {
  position: absolute;
  top: 0.5rem;
  left: 0.5rem;
  width: 1.5rem;
  height: 1.5rem;
  border: none;
  background: rgba(0, 0, 0, 0.1);
  color: currentColor;
  border-radius: 0.25rem;
  cursor: pointer;
  font-size: 1rem;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: background-color 0.2s ease;
}

.close-btn:hover {
  background: rgba(0, 0, 0, 0.2);
}

.card-content {
  padding: 1.5rem;
  background: white;
  min-height: 400px;
  display: none;
}

.card.selected .card-content {
  display: block;
  animation: fadeIn 0.7s ease;
}

@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

.description {
  color: #78716c;
  margin-bottom: 1rem;
  line-height: 1.6;
  font-size: 1.125rem;
}

.files-section h4 {
  font-weight: 600;
  color: #44403c;
  margin-bottom: 0.75rem;
  font-size: 1.125rem;
}

.files-list {
  max-height: 24rem;
  overflow-y: auto;
  margin-bottom: 1.5rem;
}

.file-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0.75rem;
  background: #fafaf9;
  border-radius: 0.5rem;
  margin-bottom: 0.5rem;
  transition: background-color 0.2s ease;
}

.file-item:hover {
  background: #f5f5f4;
}

.file-info {
  display: flex;
  align-items: center;
  gap: 0.75rem;
}

.file-icon {
  font-size: 1rem;
  color: #78716c;
}

.file-name {
  font-weight: 500;
  color: #44403c;
  margin-bottom: 0.125rem;
}

.file-meta {
  font-size: 0.875rem;
  color: #78716c;
}

.file-actions {
  display: flex;
  gap: 0.25rem;
}

.action-btn {
  width: 2rem;
  height: 2rem;
  border: none;
  background: transparent;
  cursor: pointer;
  border-radius: 0.25rem;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: background-color 0.2s ease;
}

.action-btn:hover {
  background: #e7e5e4;
}

.card-actions {
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
  animation: fadeIn 0.5s ease;
}

.primary-btn,
.secondary-btn {
  flex: 1;
  padding: 0.75rem 1rem;
  border-radius: 0.375rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
}

.primary-btn {
  background: #44403c;
  color: white;
  border: none;
}

.primary-btn:hover {
  background: #57534e;
}

.secondary-btn {
  background: transparent;
  color: #44403c;
  border: 1px solid #d6d3d1;
}

.secondary-btn:hover {
  background: #fafaf9;
}

.ghost-btn {
  width: 100%;
  padding: 0.75rem 1rem;
  background: transparent;
  color: #78716c;
  border: none;
  border-radius: 0.375rem;
  cursor: pointer;
  transition: background-color 0.2s ease;
}

.ghost-btn:hover {
  background: #f5f5f4;
}

/* Stage-specific colors */
.stage-one {
  background-color: #fbbf24;
  color: #78350f;
}

.stage-two {
  background-color: #c4b5fd;
  color: #581c87;
}

.stage-three {
  background-color: #e7e5e4;
  color: #44403c;
}

.stage-four {
  background-color: #f3f4f6;
  color: #374151;
}

.stage-five {
  background-color: #a3e635;
  color: #365314;
}

.stage-six {
  background-color: #fb923c;
  color: #9a3412;
}

.stage-seven {
  background-color: #92400e;
  color: #fef3c7;
}

/* 响应式设计 - 项目阶段管理 */
@media (max-width: 1200px) {
  .card.selected {
    transform: translateX(80%) scale(1.02);
    width: 450px;
    max-width: 450px;
  }
}

@media (max-width: 1024px) {
  .card {
    max-width: 400px;
  }

  .card.selected {
    transform: translateX(60%) scale(1);
    width: 420px;
    max-width: 420px;
  }
}

@media (max-width: 768px) {
  #page5 {
    padding: 1rem;
  }

  .project-management-container {
    padding: 1rem;
  }

  .cards-container {
    justify-content: center;
  }

  .card {
    max-width: 350px;
  }

  .card.selected {
    transform: translateX(0) scale(1);
    width: 100%;
    max-width: 100%;
    position: fixed;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%) scale(1);
    max-height: 80vh;
    overflow-y: auto;
  }

  .project-header h1 {
    font-size: 1.5rem;
  }

  .stage-name {
    font-size: 1rem;
  }

  .stage-title {
    font-size: 0.9rem;
  }

  .card-content {
    padding: 1rem;
    min-height: 300px;
  }

  .description {
    font-size: 1rem;
  }

  .files-section h4 {
    font-size: 1rem;
  }

  /* 移动端背景遮罩 */
  .card.selected::before {
    content: '';
    position: fixed;
    top: 0;
    left: 0;
    width: 100vw;
    height: 100vh;
    background: rgba(0, 0, 0, 0.5);
    z-index: -1;
  }
}

/* ==================== 叶子装饰样式 ==================== */
.leaf-decorations {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  pointer-events: none;
  z-index: 1;
  overflow: hidden;
}

.leaf-element {
  position: absolute;
  pointer-events: none;
  opacity: 0.8;
  transition: all 0.3s ease;
  will-change: transform;
}

.leaf-svg {
  width: 100%;
  height: 100%;
  filter: drop-shadow(0 4px 8px rgba(0, 0, 0, 0.1));
}

/* 叶子尺寸 */
.leaf-large {
  width: 400px;
  height: 500px;
}

.leaf-medium {
  width: 250px;
  height: 320px;
}

.leaf-small {
  width: 150px;
  height: 200px;
}

/* 第4页叶子位置 */
#page4 .leaf-right {
  top: 10%;
  right: -50px;
  transform: rotate(15deg);
  animation: leafFloat 8s ease-in-out infinite;
}

#page4 .leaf-left {
  top: 60%;
  left: -30px;
  transform: rotate(-25deg);
  animation: leafFloat 6s ease-in-out infinite reverse;
}

/* 第5页叶子位置 */
#page5 .leaf-top-right {
  top: -80px;
  right: -80px;
  transform: rotate(25deg);
  animation: leafSway 10s ease-in-out infinite;
}

#page5 .leaf-bottom-left {
  bottom: -50px;
  left: -40px;
  transform: rotate(-15deg);
  animation: leafFloat 7s ease-in-out infinite;
}

#page5 .leaf-bottom-right {
  bottom: 15%;
  right: 8%;
  transform: rotate(35deg);
  animation: leafSway 5s ease-in-out infinite reverse;
}

/* 叶子动画 */
@keyframes leafFloat {
  0%, 100% {
    transform: translateY(0px) rotate(var(--initial-rotation, 0deg));
  }
  50% {
    transform: translateY(-20px) rotate(calc(var(--initial-rotation, 0deg) + 5deg));
  }
}

@keyframes leafSway {
  0%, 100% {
    transform: translateX(0px) rotate(var(--initial-rotation, 0deg));
  }
  25% {
    transform: translateX(10px) rotate(calc(var(--initial-rotation, 0deg) + 3deg));
  }
  75% {
    transform: translateX(-10px) rotate(calc(var(--initial-rotation, 0deg) - 3deg));
  }
}

/* 鼠标悬停效果 */
.page-section:hover .leaf-element {
  opacity: 1;
  transform: scale(1.02);
}

/* 第5页内容样式 */
.page5-content {
  position: relative;
  z-index: 2;
  text-align: center;
  color: white;
}

.page5-content .main-title {
  font-size: 4rem;
  font-weight: 700;
  line-height: 0.9;
  margin-bottom: 2rem;
}

.page5-content .title-line {
  display: block;
  color: white;
  text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.3);
}

.page5-content .title-line.highlight {
  color: #fff;
  text-shadow:
    0 0 10px rgba(255, 255, 255, 0.5),
    2px 2px 4px rgba(0, 0, 0, 0.3);
}

.page5-content .page5-subtitle {
  font-size: 1.2rem;
  color: rgba(255, 255, 255, 0.9);
  text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.3);
}

/* 叶子装饰响应式设计 */
@media (max-width: 1200px) {
  .leaf-large {
    width: 320px;
    height: 400px;
  }

  .leaf-medium {
    width: 200px;
    height: 260px;
  }

  .leaf-small {
    width: 120px;
    height: 160px;
  }
}

@media (max-width: 768px) {
  .leaf-decorations {
    display: none; /* 在移动设备上隐藏叶子装饰 */
  }

  .page5-content .main-title {
    font-size: 2.5rem;
  }

  .page5-content .page5-subtitle {
    font-size: 1rem;
  }
}

@media (max-width: 480px) {
  .page5-content .main-title {
    font-size: 2rem;
  }
}
